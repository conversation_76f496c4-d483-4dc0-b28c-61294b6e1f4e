# merit-ios

This App is organized into multiple modules including:

- Main app (Merit) with DEV/UAT/PROD targets
- Core modules: APILayer, Storage, SharedData, CUIModule
- Feature modules: Home, Market, Wallet, WealthPlan, History, Authentication, etc.

The app uses CocoaPods for dependency management with RxSwift and XCoordinator for navigation. The app supports multiple environments (DEV/UAT/PROD) and includes push notification integration via JPush.

```
pod deintegrate
pod install
```
