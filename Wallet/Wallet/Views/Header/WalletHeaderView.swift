//
//  WalletHeaderView.swift
//  Wallet
//
//  Created by <PERSON><PERSON><PERSON> on 12/19/66.
//
import CUIModule
import UIKit
import Core
import RxSwift
import RxCocoa
import SharedData

final class WalletHeaderView: UIView, AnyView, LocalizeNeededView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleStackView,
                                       balanceStackView,
                                       gainLossStackView,
                                       cashStackView])
        
        return stackView
    }()
    
    private lazy var titleStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)
        stackView.addArrangedSubviews([balanceTitleLabel,
                                       infoButton])
        infoButton.snp.makeConstraints { $0.width.height.equalTo(18) }
        
        return stackView
    }()
    
    private lazy var balanceTitleLabel = UILabel(font: Font.regular.of(size: 12),
                                                 textColor: Color.txtDisabled)
    
    fileprivate lazy var infoButton = UIButton(normalImage: .image(named: "merit_ic_info"))
    
    private lazy var balanceStackView = {
        let stackView = UIStackView(alignment: .center,
                                     spacing: 4)
        stackView.addArrangedSubviews([balanceLabel,
                                       currencyWithEyeStackView])

        return stackView
    }()

    private lazy var currencyWithEyeStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 6)
        stackView.addArrangedSubviews([currencyLabel,
                                       privacyToggleButton])

        return stackView
    }()

    private lazy var balanceLabel = UILabel(font: Font.semiBold.of(size: 24),
                                            textColor: Color.txtTitle)

    private lazy var currencyLabel = UILabel(font: Font.regular.of(size: 12),
                                             textColor: Color.txtInactive)

    private lazy var privacyToggleButton = {
        let button = UIButton()
        button.setImage(.image(named: "merit_eye"), for: .normal)
        button.setImage(.image(named: "merit_eye_slash"), for: .selected)
        button.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }

        // Accessibility support
        button.accessibilityLabel = "Toggle portfolio privacy"
        button.accessibilityHint = "Tap to show or hide financial data"

        return button
    }()
    
    private lazy var gainLossStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)
        stackView.addArrangedSubviews([unrealizedGLView,
                                       unrealizedGLPercentView])
        unrealizedGLPercentView.snp.makeConstraints { $0.height.equalTo(14) }
        
        return stackView
    }()
    
    private lazy var unrealizedGLView = PriceChangeView()
    private lazy var unrealizedGLPercentView = PercentChangeBadge()
    
    private lazy var cashStackView = {
        let stackView = UIStackView(alignment: .firstBaseline,
                                     spacing: 4)
        stackView.addArrangedSubviews([cashTitleLabel,
                                       cashBalanceLabel,
                                       cashCurrencyLabel])
        
        return stackView
    }()
    
    private lazy var cashTitleLabel = UILabel(font: Font.semiBold.of(size: 10),
                                         textColor: Color.txtGolden)
    
    private lazy var cashBalanceLabel = UILabel(font: Font.semiBold.of(size: 16),
                                                textColor: Color.txtGray)
    
    private lazy var cashCurrencyLabel = UILabel(font: Font.regular.of(size: 9),
                                                 textColor: Color.txtInactive)
    
    fileprivate lazy var downloadButton = UIButton(backgroundColor: .clear,
                                                   normalImage: .image(named: "merit_ic_download"))
    
    // MARK: Properties
    var hideInfoButton: Bool = false {
        didSet { infoButton.isHidden = hideInfoButton }
    }  
    var hideDownloadButton: Bool = false {
        didSet { downloadButton.isHidden = hideDownloadButton }
    }
    
    private let disposeBag = DisposeBag()
    private var currentDisplayModel: DisplayModel?

    // MARK: Life cycle
    init(_ data: DisplayModel = .empty) {
        super.init(frame: .zero)

        setupUI()
        bindPrivacyToggle()
        setData(data)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func setupUI() {
        backgroundColor = .clear
        
        cashTitleLabel.backgroundColor = Color.cashBg
        
        addSubviews([contentStackView,
                     downloadButton])
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(16)
            make.left.equalTo(12)
            make.right.equalTo(-12)
            make.bottom.equalTo(-8)
        }
        
        downloadButton.snp.makeConstraints { make in
            make.top.right.equalToSuperview()
            make.width.height.equalTo(44)
        }

        setTexts()
    }

    func bindPrivacyToggle() {
        // Bind privacy toggle button tap
        privacyToggleButton.addTarget(self, action: #selector(privacyToggleButtonTapped), for: .touchUpInside)

        // Set initial state
        updatePrivacyButtonState()

        // Listen for privacy state changes from other screens
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(privacyStateChanged),
            name: NSNotification.Name("PrivacyModeChanged"),
            object: nil
        )
    }

    @objc private func privacyToggleButtonTapped() {
        PrivacyManager.shared.togglePrivacyMode()
        updatePrivacyButtonState()

        // Refresh display with current data
        if let displayModel = currentDisplayModel {
            updateDisplayWithPrivacy(displayModel)
        }

        // Notify other screens of the change
        NotificationCenter.default.post(name: NSNotification.Name("PrivacyModeChanged"), object: nil)
    }

    @objc private func privacyStateChanged() {
        updatePrivacyButtonState()

        // Refresh display with current data
        if let displayModel = currentDisplayModel {
            updateDisplayWithPrivacy(displayModel)
        }
    }

    private func updatePrivacyButtonState() {
        let isEnabled = PrivacyManager.shared.currentPrivacyState
        privacyToggleButton.isSelected = isEnabled
        privacyToggleButton.accessibilityValue = isEnabled ? "Hidden" : "Visible"
    }

    func setTexts() {
        balanceTitleLabel.text = "key0615".localized()
        cashTitleLabel.text = "key1056".localized()
    }

    func setData(_ data: DisplayModel) {
        currentDisplayModel = data
        updateDisplayWithPrivacy(data)
    }

    private func updateDisplayWithPrivacy(_ data: DisplayModel) {
        let isPrivacyEnabled = PrivacyManager.shared.currentPrivacyState

        // Update balance display
        balanceLabel.text = isPrivacyEnabled ? "****" : data.balance.formatted(digits: 2)
        cashBalanceLabel.text = isPrivacyEnabled ? "****" : data.cash.formatted(digits: 2)
        // keep currency as the same
        currencyLabel.text =  data.currency
        cashCurrencyLabel.text =  data.currency

        let changeDirection = PriceChangeDirection.from(priceChange: data.exchange,
                                                        percent: data.rate)

        // Update gain/loss values
        if isPrivacyEnabled {
            unrealizedGLView.updateValueWithPrivacy(changeDirection: changeDirection)
            unrealizedGLPercentView.updateValueWithPrivacy(changeDirection: changeDirection)
        } else {
            unrealizedGLView.updateValue(data.exchange, changeDirection: changeDirection)
            unrealizedGLPercentView.updateValue(data.rate, changeDirection: changeDirection)
        }
    }
    
    func updateFontStyle(balanceFont: UIFont,
                         currencyFont: UIFont) {
        balanceLabel.font = balanceFont
        currencyLabel.font = currencyFont
    }
}

// MARK: - Display Model
extension WalletHeaderView {
    
    struct DisplayModel {
        let balance: Double
        let currency: String
        let exchange: Double
        let rate: Double
        let cash: Double
        
        static let empty = DisplayModel(balance: 0,
                                        currency: "USD",
                                        exchange: 0,
                                        rate: 0,
                                        cash:0)
    }
}

// MARK: - extension Reactive
extension Reactive where Base: WalletHeaderView {
    
    var tapInfo: Observable<Void> {
        base.infoButton.rx.tap.asObservable()
    }
    
    var tapDownload: Observable<Void> {
        base.downloadButton.rx.tap.mapToVoid()
    }
}
