//
//  CommonUtils.swift
//  SharedData
//
//  Created by <PERSON> on 28/12/2023.
//

import Foundation

public extension Optional where Wrapped == String {
    
    func toNumberOrNil() -> Double? {
        guard let value = self, !value.isEmpty else { return nil }
        
        return Double(value)
    }
    
    func toNilableNumber() -> Double? {
        guard let self = self, !self.isEmpty else { return nil}
        
        return self.toNumber()
    }
}

// MARK: - extension Double Optional
public extension Optional where Wrapped == Double {
    
    var wrappedValue: Double {
        self ?? .zero
    }
    
    /// To format string.
    /// - Parameters:
    ///   - formatIfNil: To display when value is nil. Default is empty
    ///   - showPositive: To display plus (+) sign. Default is true
    ///   - prefix: To display at the end of the string. Default is empty
    ///   - digits: How many decimal points to use, default decimal is 2 digits.
    /// - Returns: Formatted  string.
    func toString(formatIfNil: String = "", showPositive: Bool = true, prefix: String = "", digits: Int = 2) -> String {
        guard let value = self else { return formatIfNil }
        return value.toString(showPositive: showPositive, prefix: prefix, digits: digits)
    }
}

// MARK: - PrivacyManager
public final class PrivacyManager {

    public static let shared = PrivacyManager()

    // MARK: - Properties
    private var _isPrivacyModeEnabled: Bool = false

    // MARK: - Public Interface
    public var currentPrivacyState: Bool {
        return _isPrivacyModeEnabled
    }

    // MARK: - Initialization
    private init() {
        // Load saved privacy state from UserDefaults
        _isPrivacyModeEnabled = UserDefaults.standard.bool(forKey: "portfolio_privacy_mode_enabled")
    }

    // MARK: - Public Methods
    public func togglePrivacyMode() {
        let newState = !_isPrivacyModeEnabled
        setPrivacyMode(enabled: newState)
    }

    public func setPrivacyMode(enabled: Bool) {
        _isPrivacyModeEnabled = enabled

        // Persist the state
        UserDefaults.standard.set(enabled, forKey: "portfolio_privacy_mode_enabled")
        UserDefaults.standard.synchronize()
    }

    // MARK: - Utility Methods
    public func maskFinancialValue(_ value: String?) -> String {
        guard currentPrivacyState, let value = value, !value.isEmpty else {
            return value ?? "-"
        }
        return "****"
    }

    public func maskFinancialValue(_ value: Double?) -> String {
        guard currentPrivacyState else {
            return value?.formatted(digits: 2) ?? "-"
        }
        return "****"
    }

    public func maskCurrency(_ currency: String?) -> String {
        guard currentPrivacyState, let currency = currency, !currency.isEmpty else {
            return currency ?? ""
        }
        return "***"
    }
}
