// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0E2A8C9C77623B37DC3828CE /* Pods_Merit_PROD.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1FACA542BF6C4EF7D0177D74 /* Pods_Merit_PROD.framework */; };
		83A64E4E2D39570900E9F3BE /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 83A64E4C2D39570900E9F3BE /* InfoPlist.strings */; };
		83A64E4F2D39570900E9F3BE /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 83A64E4C2D39570900E9F3BE /* InfoPlist.strings */; };
		83A64E502D39570900E9F3BE /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 83A64E4C2D39570900E9F3BE /* InfoPlist.strings */; };
		DF0ADF9A2BD76BF40098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF882BD76BF40098FDAF /* APILayer.framework */; };
		DF0ADF9B2BD76BF40098FDAF /* APILayer.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF882BD76BF40098FDAF /* APILayer.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADF9C2BD76BF40098FDAF /* AppSetting.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF892BD76BF40098FDAF /* AppSetting.framework */; };
		DF0ADF9D2BD76BF40098FDAF /* AppSetting.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF892BD76BF40098FDAF /* AppSetting.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADF9E2BD76BF40098FDAF /* Authentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8A2BD76BF40098FDAF /* Authentication.framework */; };
		DF0ADF9F2BD76BF40098FDAF /* Authentication.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8A2BD76BF40098FDAF /* Authentication.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFA02BD76BF40098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8B2BD76BF40098FDAF /* CUIModule.framework */; };
		DF0ADFA12BD76BF40098FDAF /* CUIModule.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8B2BD76BF40098FDAF /* CUIModule.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFA22BD76BF40098FDAF /* DGCharts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8C2BD76BF40098FDAF /* DGCharts.framework */; };
		DF0ADFA32BD76BF40098FDAF /* DGCharts.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8C2BD76BF40098FDAF /* DGCharts.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFA42BD76BF40098FDAF /* History.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8D2BD76BF40098FDAF /* History.framework */; };
		DF0ADFA52BD76BF40098FDAF /* History.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8D2BD76BF40098FDAF /* History.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFA62BD76BF50098FDAF /* Home.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8E2BD76BF40098FDAF /* Home.framework */; };
		DF0ADFA72BD76BF50098FDAF /* Home.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8E2BD76BF40098FDAF /* Home.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFA82BD76BF50098FDAF /* InstrumentSearch.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8F2BD76BF40098FDAF /* InstrumentSearch.framework */; };
		DF0ADFA92BD76BF50098FDAF /* InstrumentSearch.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF8F2BD76BF40098FDAF /* InstrumentSearch.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFAA2BD76BF50098FDAF /* Login.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF902BD76BF40098FDAF /* Login.framework */; };
		DF0ADFAB2BD76BF50098FDAF /* Login.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF902BD76BF40098FDAF /* Login.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFAC2BD76BF50098FDAF /* Market.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF912BD76BF40098FDAF /* Market.framework */; };
		DF0ADFAD2BD76BF50098FDAF /* Market.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF912BD76BF40098FDAF /* Market.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFAE2BD76BF50098FDAF /* MarketDashboard.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF922BD76BF40098FDAF /* MarketDashboard.framework */; };
		DF0ADFAF2BD76BF50098FDAF /* MarketDashboard.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF922BD76BF40098FDAF /* MarketDashboard.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFB02BD76BF50098FDAF /* Notification.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF932BD76BF40098FDAF /* Notification.framework */; };
		DF0ADFB12BD76BF50098FDAF /* Notification.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF932BD76BF40098FDAF /* Notification.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFB22BD76BF50098FDAF /* Onboarding.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF942BD76BF40098FDAF /* Onboarding.framework */; };
		DF0ADFB32BD76BF50098FDAF /* Onboarding.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF942BD76BF40098FDAF /* Onboarding.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFB42BD76BF50098FDAF /* ReportGeneration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF952BD76BF40098FDAF /* ReportGeneration.framework */; };
		DF0ADFB52BD76BF50098FDAF /* ReportGeneration.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF952BD76BF40098FDAF /* ReportGeneration.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFB62BD76BF50098FDAF /* SharedData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF962BD76BF40098FDAF /* SharedData.framework */; };
		DF0ADFB72BD76BF50098FDAF /* SharedData.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF962BD76BF40098FDAF /* SharedData.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFB82BD76BF50098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF972BD76BF40098FDAF /* Storage.framework */; };
		DF0ADFB92BD76BF50098FDAF /* Storage.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF972BD76BF40098FDAF /* Storage.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFBA2BD76BF50098FDAF /* Wallet.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF982BD76BF40098FDAF /* Wallet.framework */; };
		DF0ADFBB2BD76BF50098FDAF /* Wallet.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF982BD76BF40098FDAF /* Wallet.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFBC2BD76BF50098FDAF /* WealthPlan.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF992BD76BF40098FDAF /* WealthPlan.framework */; };
		DF0ADFBD2BD76BF50098FDAF /* WealthPlan.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADF992BD76BF40098FDAF /* WealthPlan.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFD12BD76C1A0098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFBF2BD76C1A0098FDAF /* APILayer.framework */; };
		DF0ADFD22BD76C1A0098FDAF /* APILayer.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFBF2BD76C1A0098FDAF /* APILayer.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFD32BD76C1A0098FDAF /* AppSetting.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC02BD76C1A0098FDAF /* AppSetting.framework */; };
		DF0ADFD42BD76C1A0098FDAF /* AppSetting.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC02BD76C1A0098FDAF /* AppSetting.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFD52BD76C1A0098FDAF /* Authentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC12BD76C1A0098FDAF /* Authentication.framework */; };
		DF0ADFD62BD76C1A0098FDAF /* Authentication.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC12BD76C1A0098FDAF /* Authentication.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFD72BD76C1A0098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC22BD76C1A0098FDAF /* CUIModule.framework */; };
		DF0ADFD82BD76C1A0098FDAF /* CUIModule.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC22BD76C1A0098FDAF /* CUIModule.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFD92BD76C1A0098FDAF /* DGCharts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC32BD76C1A0098FDAF /* DGCharts.framework */; };
		DF0ADFDA2BD76C1A0098FDAF /* DGCharts.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC32BD76C1A0098FDAF /* DGCharts.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFDB2BD76C1B0098FDAF /* History.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC42BD76C1A0098FDAF /* History.framework */; };
		DF0ADFDC2BD76C1B0098FDAF /* History.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC42BD76C1A0098FDAF /* History.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFDD2BD76C1B0098FDAF /* Home.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC52BD76C1A0098FDAF /* Home.framework */; };
		DF0ADFDE2BD76C1B0098FDAF /* Home.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC52BD76C1A0098FDAF /* Home.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFDF2BD76C1B0098FDAF /* InstrumentSearch.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC62BD76C1A0098FDAF /* InstrumentSearch.framework */; };
		DF0ADFE02BD76C1B0098FDAF /* InstrumentSearch.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC62BD76C1A0098FDAF /* InstrumentSearch.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFE12BD76C1B0098FDAF /* Login.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC72BD76C1A0098FDAF /* Login.framework */; };
		DF0ADFE22BD76C1B0098FDAF /* Login.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC72BD76C1A0098FDAF /* Login.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFE32BD76C1B0098FDAF /* Market.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC82BD76C1A0098FDAF /* Market.framework */; };
		DF0ADFE42BD76C1B0098FDAF /* Market.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC82BD76C1A0098FDAF /* Market.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFE52BD76C1B0098FDAF /* MarketDashboard.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC92BD76C1A0098FDAF /* MarketDashboard.framework */; };
		DF0ADFE62BD76C1B0098FDAF /* MarketDashboard.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFC92BD76C1A0098FDAF /* MarketDashboard.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFE72BD76C1B0098FDAF /* Notification.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCA2BD76C1A0098FDAF /* Notification.framework */; };
		DF0ADFE82BD76C1B0098FDAF /* Notification.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCA2BD76C1A0098FDAF /* Notification.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFE92BD76C1B0098FDAF /* Onboarding.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCB2BD76C1A0098FDAF /* Onboarding.framework */; };
		DF0ADFEA2BD76C1B0098FDAF /* Onboarding.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCB2BD76C1A0098FDAF /* Onboarding.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFEB2BD76C1B0098FDAF /* ReportGeneration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCC2BD76C1A0098FDAF /* ReportGeneration.framework */; };
		DF0ADFEC2BD76C1B0098FDAF /* ReportGeneration.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCC2BD76C1A0098FDAF /* ReportGeneration.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFED2BD76C1B0098FDAF /* SharedData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCD2BD76C1A0098FDAF /* SharedData.framework */; };
		DF0ADFEE2BD76C1B0098FDAF /* SharedData.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCD2BD76C1A0098FDAF /* SharedData.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFEF2BD76C1C0098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCE2BD76C1A0098FDAF /* Storage.framework */; };
		DF0ADFF02BD76C1C0098FDAF /* Storage.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCE2BD76C1A0098FDAF /* Storage.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFF12BD76C1C0098FDAF /* Wallet.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCF2BD76C1A0098FDAF /* Wallet.framework */; };
		DF0ADFF22BD76C1C0098FDAF /* Wallet.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFCF2BD76C1A0098FDAF /* Wallet.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0ADFF32BD76C1C0098FDAF /* WealthPlan.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFD02BD76C1A0098FDAF /* WealthPlan.framework */; };
		DF0ADFF42BD76C1C0098FDAF /* WealthPlan.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFD02BD76C1A0098FDAF /* WealthPlan.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0082BD76C360098FDAF /* APILayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF62BD76C360098FDAF /* APILayer.framework */; };
		DF0AE0092BD76C360098FDAF /* APILayer.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF62BD76C360098FDAF /* APILayer.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE00A2BD76C360098FDAF /* AppSetting.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF72BD76C360098FDAF /* AppSetting.framework */; };
		DF0AE00B2BD76C360098FDAF /* AppSetting.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF72BD76C360098FDAF /* AppSetting.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE00C2BD76C370098FDAF /* Authentication.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF82BD76C360098FDAF /* Authentication.framework */; };
		DF0AE00D2BD76C370098FDAF /* Authentication.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF82BD76C360098FDAF /* Authentication.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE00E2BD76C370098FDAF /* CUIModule.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF92BD76C360098FDAF /* CUIModule.framework */; };
		DF0AE00F2BD76C370098FDAF /* CUIModule.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFF92BD76C360098FDAF /* CUIModule.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0102BD76C370098FDAF /* DGCharts.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFA2BD76C360098FDAF /* DGCharts.framework */; };
		DF0AE0112BD76C370098FDAF /* DGCharts.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFA2BD76C360098FDAF /* DGCharts.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0122BD76C370098FDAF /* History.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFB2BD76C360098FDAF /* History.framework */; };
		DF0AE0132BD76C370098FDAF /* History.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFB2BD76C360098FDAF /* History.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0142BD76C370098FDAF /* Home.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFC2BD76C360098FDAF /* Home.framework */; };
		DF0AE0152BD76C370098FDAF /* Home.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFC2BD76C360098FDAF /* Home.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0162BD76C370098FDAF /* InstrumentSearch.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFD2BD76C360098FDAF /* InstrumentSearch.framework */; };
		DF0AE0172BD76C370098FDAF /* InstrumentSearch.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFD2BD76C360098FDAF /* InstrumentSearch.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0182BD76C370098FDAF /* Login.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFE2BD76C360098FDAF /* Login.framework */; };
		DF0AE0192BD76C370098FDAF /* Login.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFE2BD76C360098FDAF /* Login.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE01A2BD76C370098FDAF /* Market.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFF2BD76C360098FDAF /* Market.framework */; };
		DF0AE01B2BD76C370098FDAF /* Market.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0ADFFF2BD76C360098FDAF /* Market.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE01C2BD76C370098FDAF /* MarketDashboard.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0002BD76C360098FDAF /* MarketDashboard.framework */; };
		DF0AE01D2BD76C370098FDAF /* MarketDashboard.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0002BD76C360098FDAF /* MarketDashboard.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE01E2BD76C380098FDAF /* Notification.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0012BD76C360098FDAF /* Notification.framework */; };
		DF0AE01F2BD76C380098FDAF /* Notification.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0012BD76C360098FDAF /* Notification.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0202BD76C380098FDAF /* Onboarding.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0022BD76C360098FDAF /* Onboarding.framework */; };
		DF0AE0212BD76C380098FDAF /* Onboarding.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0022BD76C360098FDAF /* Onboarding.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0222BD76C380098FDAF /* ReportGeneration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0032BD76C360098FDAF /* ReportGeneration.framework */; };
		DF0AE0232BD76C380098FDAF /* ReportGeneration.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0032BD76C360098FDAF /* ReportGeneration.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0242BD76C380098FDAF /* SharedData.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0042BD76C360098FDAF /* SharedData.framework */; };
		DF0AE0252BD76C380098FDAF /* SharedData.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0042BD76C360098FDAF /* SharedData.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0262BD76C380098FDAF /* Storage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0052BD76C360098FDAF /* Storage.framework */; };
		DF0AE0272BD76C380098FDAF /* Storage.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0052BD76C360098FDAF /* Storage.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0282BD76C380098FDAF /* Wallet.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0062BD76C360098FDAF /* Wallet.framework */; };
		DF0AE0292BD76C380098FDAF /* Wallet.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0062BD76C360098FDAF /* Wallet.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE02A2BD76C380098FDAF /* WealthPlan.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0072BD76C360098FDAF /* WealthPlan.framework */; };
		DF0AE02B2BD76C380098FDAF /* WealthPlan.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = DF0AE0072BD76C360098FDAF /* WealthPlan.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		DF0AE0FB2BD7753B0098FDAF /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F42BD7753B0098FDAF /* AppCoordinator.swift */; };
		DF0AE0FC2BD7753B0098FDAF /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F42BD7753B0098FDAF /* AppCoordinator.swift */; };
		DF0AE0FD2BD7753B0098FDAF /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F42BD7753B0098FDAF /* AppCoordinator.swift */; };
		DF0AE0FE2BD7753B0098FDAF /* MainTabbarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F52BD7753B0098FDAF /* MainTabbarController.swift */; };
		DF0AE0FF2BD7753B0098FDAF /* MainTabbarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F52BD7753B0098FDAF /* MainTabbarController.swift */; };
		DF0AE1002BD7753B0098FDAF /* MainTabbarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F52BD7753B0098FDAF /* MainTabbarController.swift */; };
		DF0AE1012BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F62BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift */; };
		DF0AE1022BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F62BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift */; };
		DF0AE1032BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F62BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift */; };
		DF0AE1042BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F72BD7753B0098FDAF /* MainTabbarCoordinator.swift */; };
		DF0AE1052BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F72BD7753B0098FDAF /* MainTabbarCoordinator.swift */; };
		DF0AE1062BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F72BD7753B0098FDAF /* MainTabbarCoordinator.swift */; };
		DF0AE1072BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F92BD7753B0098FDAF /* SplashScreenViewModel.swift */; };
		DF0AE1082BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F92BD7753B0098FDAF /* SplashScreenViewModel.swift */; };
		DF0AE1092BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0F92BD7753B0098FDAF /* SplashScreenViewModel.swift */; };
		DF0AE10A2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0FA2BD7753B0098FDAF /* SplashScreenViewController.swift */; };
		DF0AE10B2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0FA2BD7753B0098FDAF /* SplashScreenViewController.swift */; };
		DF0AE10C2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF0AE0FA2BD7753B0098FDAF /* SplashScreenViewController.swift */; };
		DF273C7A2BD75E7C003F9464 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273C792BD75E7C003F9464 /* AppDelegate.swift */; };
		DF273C832BD75E7D003F9464 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DF273C822BD75E7D003F9464 /* Assets.xcassets */; };
		DF273C862BD75E7D003F9464 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DF273C842BD75E7D003F9464 /* LaunchScreen.storyboard */; };
		DF273CA52BD76258003F9464 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273C792BD75E7C003F9464 /* AppDelegate.swift */; };
		DF273CAA2BD76258003F9464 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DF273C842BD75E7D003F9464 /* LaunchScreen.storyboard */; };
		DF273CAB2BD76258003F9464 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DF273C822BD75E7D003F9464 /* Assets.xcassets */; };
		DF273CB72BD76259003F9464 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273C792BD75E7C003F9464 /* AppDelegate.swift */; };
		DF273CBC2BD76259003F9464 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DF273C842BD75E7D003F9464 /* LaunchScreen.storyboard */; };
		DF273CBD2BD76259003F9464 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DF273C822BD75E7D003F9464 /* Assets.xcassets */; };
		DF273CC72BD76441003F9464 /* main.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC52BD76441003F9464 /* main.swift */; };
		DF273CC82BD76441003F9464 /* main.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC52BD76441003F9464 /* main.swift */; };
		DF273CC92BD76441003F9464 /* main.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC52BD76441003F9464 /* main.swift */; };
		DF273CCA2BD76441003F9464 /* MainUIApplication.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC62BD76441003F9464 /* MainUIApplication.swift */; };
		DF273CCB2BD76441003F9464 /* MainUIApplication.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC62BD76441003F9464 /* MainUIApplication.swift */; };
		DF273CCC2BD76441003F9464 /* MainUIApplication.swift in Sources */ = {isa = PBXBuildFile; fileRef = DF273CC62BD76441003F9464 /* MainUIApplication.swift */; };
		F7DEA0AAC9F4EBFB30353F0E /* Pods_Merit_UAT.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3C33F350B8969484CD6AF5B4 /* Pods_Merit_UAT.framework */; };
		FCC41598AB395FC9796B50DF /* Pods_Merit_DEV.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3926C4470D2252AE05FE2859 /* Pods_Merit_DEV.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		DF0ADFBE2BD76BF60098FDAF /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				DF0ADFA72BD76BF50098FDAF /* Home.framework in Embed Frameworks */,
				DF0ADF9F2BD76BF40098FDAF /* Authentication.framework in Embed Frameworks */,
				DF0ADFBD2BD76BF50098FDAF /* WealthPlan.framework in Embed Frameworks */,
				DF0ADFB92BD76BF50098FDAF /* Storage.framework in Embed Frameworks */,
				DF0ADFAF2BD76BF50098FDAF /* MarketDashboard.framework in Embed Frameworks */,
				DF0ADF9B2BD76BF40098FDAF /* APILayer.framework in Embed Frameworks */,
				DF0ADFBB2BD76BF50098FDAF /* Wallet.framework in Embed Frameworks */,
				DF0ADFB72BD76BF50098FDAF /* SharedData.framework in Embed Frameworks */,
				DF0ADFA12BD76BF40098FDAF /* CUIModule.framework in Embed Frameworks */,
				DF0ADFB12BD76BF50098FDAF /* Notification.framework in Embed Frameworks */,
				DF0ADFA52BD76BF40098FDAF /* History.framework in Embed Frameworks */,
				DF0ADFA92BD76BF50098FDAF /* InstrumentSearch.framework in Embed Frameworks */,
				DF0ADFB32BD76BF50098FDAF /* Onboarding.framework in Embed Frameworks */,
				DF0ADFAD2BD76BF50098FDAF /* Market.framework in Embed Frameworks */,
				DF0ADFA32BD76BF40098FDAF /* DGCharts.framework in Embed Frameworks */,
				DF0ADFAB2BD76BF50098FDAF /* Login.framework in Embed Frameworks */,
				DF0ADFB52BD76BF50098FDAF /* ReportGeneration.framework in Embed Frameworks */,
				DF0ADF9D2BD76BF40098FDAF /* AppSetting.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF0ADFF52BD76C1C0098FDAF /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				DF0ADFDE2BD76C1B0098FDAF /* Home.framework in Embed Frameworks */,
				DF0ADFD62BD76C1A0098FDAF /* Authentication.framework in Embed Frameworks */,
				DF0ADFF42BD76C1C0098FDAF /* WealthPlan.framework in Embed Frameworks */,
				DF0ADFF02BD76C1C0098FDAF /* Storage.framework in Embed Frameworks */,
				DF0ADFE62BD76C1B0098FDAF /* MarketDashboard.framework in Embed Frameworks */,
				DF0ADFD22BD76C1A0098FDAF /* APILayer.framework in Embed Frameworks */,
				DF0ADFF22BD76C1C0098FDAF /* Wallet.framework in Embed Frameworks */,
				DF0ADFEE2BD76C1B0098FDAF /* SharedData.framework in Embed Frameworks */,
				DF0ADFD82BD76C1A0098FDAF /* CUIModule.framework in Embed Frameworks */,
				DF0ADFE82BD76C1B0098FDAF /* Notification.framework in Embed Frameworks */,
				DF0ADFDC2BD76C1B0098FDAF /* History.framework in Embed Frameworks */,
				DF0ADFE02BD76C1B0098FDAF /* InstrumentSearch.framework in Embed Frameworks */,
				DF0ADFEA2BD76C1B0098FDAF /* Onboarding.framework in Embed Frameworks */,
				DF0ADFE42BD76C1B0098FDAF /* Market.framework in Embed Frameworks */,
				DF0ADFDA2BD76C1A0098FDAF /* DGCharts.framework in Embed Frameworks */,
				DF0ADFE22BD76C1B0098FDAF /* Login.framework in Embed Frameworks */,
				DF0ADFEC2BD76C1B0098FDAF /* ReportGeneration.framework in Embed Frameworks */,
				DF0ADFD42BD76C1A0098FDAF /* AppSetting.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF0AE02C2BD76C390098FDAF /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				DF0AE0152BD76C370098FDAF /* Home.framework in Embed Frameworks */,
				DF0AE00D2BD76C370098FDAF /* Authentication.framework in Embed Frameworks */,
				DF0AE02B2BD76C380098FDAF /* WealthPlan.framework in Embed Frameworks */,
				DF0AE0272BD76C380098FDAF /* Storage.framework in Embed Frameworks */,
				DF0AE01D2BD76C370098FDAF /* MarketDashboard.framework in Embed Frameworks */,
				DF0AE0092BD76C360098FDAF /* APILayer.framework in Embed Frameworks */,
				DF0AE0292BD76C380098FDAF /* Wallet.framework in Embed Frameworks */,
				DF0AE0252BD76C380098FDAF /* SharedData.framework in Embed Frameworks */,
				DF0AE00F2BD76C370098FDAF /* CUIModule.framework in Embed Frameworks */,
				DF0AE01F2BD76C380098FDAF /* Notification.framework in Embed Frameworks */,
				DF0AE0132BD76C370098FDAF /* History.framework in Embed Frameworks */,
				DF0AE0172BD76C370098FDAF /* InstrumentSearch.framework in Embed Frameworks */,
				DF0AE0212BD76C380098FDAF /* Onboarding.framework in Embed Frameworks */,
				DF0AE01B2BD76C370098FDAF /* Market.framework in Embed Frameworks */,
				DF0AE0112BD76C370098FDAF /* DGCharts.framework in Embed Frameworks */,
				DF0AE0192BD76C370098FDAF /* Login.framework in Embed Frameworks */,
				DF0AE0232BD76C380098FDAF /* ReportGeneration.framework in Embed Frameworks */,
				DF0AE00B2BD76C360098FDAF /* AppSetting.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0315DFF076625A82CB799C5C /* Pods-Merit-UAT.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-UAT.debug.xcconfig"; path = "Target Support Files/Pods-Merit-UAT/Pods-Merit-UAT.debug.xcconfig"; sourceTree = "<group>"; };
		1E3E2A0BBFFD04D540C0AF21 /* Pods-Merit.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit.release.xcconfig"; path = "Target Support Files/Pods-Merit/Pods-Merit.release.xcconfig"; sourceTree = "<group>"; };
		1FACA542BF6C4EF7D0177D74 /* Pods_Merit_PROD.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Merit_PROD.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		31305F2255B77B5178846F78 /* Pods-Merit-PROD.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-PROD.debug.xcconfig"; path = "Target Support Files/Pods-Merit-PROD/Pods-Merit-PROD.debug.xcconfig"; sourceTree = "<group>"; };
		3926C4470D2252AE05FE2859 /* Pods_Merit_DEV.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Merit_DEV.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3C33F350B8969484CD6AF5B4 /* Pods_Merit_UAT.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Merit_UAT.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		3CC78B75BDCF86BA8B14A90A /* Pods-Merit-DEV.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-DEV.release.xcconfig"; path = "Target Support Files/Pods-Merit-DEV/Pods-Merit-DEV.release.xcconfig"; sourceTree = "<group>"; };
		577942E4C63E62C67CD9E7B8 /* Pods-Merit-DEV.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-DEV.debug.xcconfig"; path = "Target Support Files/Pods-Merit-DEV/Pods-Merit-DEV.debug.xcconfig"; sourceTree = "<group>"; };
		83443CA32D022ACD00BDC625 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		83A64E4D2D39570900E9F3BE /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		83A64E512D39571800E9F3BE /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		83A64E522D39669400E9F3BE /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		83A64E532D39669B00E9F3BE /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		83D977A52CEED7B900A6BC4D /* Merit-UAT.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Merit-UAT.entitlements"; sourceTree = "<group>"; };
		83D977A62CEED7E200A6BC4D /* Merit-PROD.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Merit-PROD.entitlements"; sourceTree = "<group>"; };
		83D977A72CEED7EA00A6BC4D /* Merit-DEV.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Merit-DEV.entitlements"; sourceTree = "<group>"; };
		83D977A82CEF2B0A00A6BC4D /* Merit-DEV-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Merit-DEV-Bridging-Header.h"; sourceTree = "<group>"; };
		83D977A92CEF2B0A00A6BC4D /* Merit-UAT-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Merit-UAT-Bridging-Header.h"; sourceTree = "<group>"; };
		83D977AA2CEF2B0A00A6BC4D /* Merit-PROD-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Merit-PROD-Bridging-Header.h"; sourceTree = "<group>"; };
		C02BCA1538DC4B0AB8B93638 /* Pods-Merit.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit.debug.xcconfig"; path = "Target Support Files/Pods-Merit/Pods-Merit.debug.xcconfig"; sourceTree = "<group>"; };
		C8F2E421371403591197C16A /* Pods-Merit-PROD.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-PROD.release.xcconfig"; path = "Target Support Files/Pods-Merit-PROD/Pods-Merit-PROD.release.xcconfig"; sourceTree = "<group>"; };
		DF0ADF882BD76BF40098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF892BD76BF40098FDAF /* AppSetting.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AppSetting.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8A2BD76BF40098FDAF /* Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8B2BD76BF40098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8C2BD76BF40098FDAF /* DGCharts.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = DGCharts.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8D2BD76BF40098FDAF /* History.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = History.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8E2BD76BF40098FDAF /* Home.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Home.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF8F2BD76BF40098FDAF /* InstrumentSearch.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = InstrumentSearch.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF902BD76BF40098FDAF /* Login.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Login.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF912BD76BF40098FDAF /* Market.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Market.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF922BD76BF40098FDAF /* MarketDashboard.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MarketDashboard.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF932BD76BF40098FDAF /* Notification.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Notification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF942BD76BF40098FDAF /* Onboarding.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Onboarding.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF952BD76BF40098FDAF /* ReportGeneration.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ReportGeneration.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF962BD76BF40098FDAF /* SharedData.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SharedData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF972BD76BF40098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF982BD76BF40098FDAF /* Wallet.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Wallet.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADF992BD76BF40098FDAF /* WealthPlan.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = WealthPlan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFBF2BD76C1A0098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC02BD76C1A0098FDAF /* AppSetting.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AppSetting.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC12BD76C1A0098FDAF /* Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC22BD76C1A0098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC32BD76C1A0098FDAF /* DGCharts.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = DGCharts.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC42BD76C1A0098FDAF /* History.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = History.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC52BD76C1A0098FDAF /* Home.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Home.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC62BD76C1A0098FDAF /* InstrumentSearch.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = InstrumentSearch.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC72BD76C1A0098FDAF /* Login.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Login.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC82BD76C1A0098FDAF /* Market.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Market.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFC92BD76C1A0098FDAF /* MarketDashboard.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MarketDashboard.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCA2BD76C1A0098FDAF /* Notification.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Notification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCB2BD76C1A0098FDAF /* Onboarding.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Onboarding.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCC2BD76C1A0098FDAF /* ReportGeneration.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ReportGeneration.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCD2BD76C1A0098FDAF /* SharedData.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SharedData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCE2BD76C1A0098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFCF2BD76C1A0098FDAF /* Wallet.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Wallet.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFD02BD76C1A0098FDAF /* WealthPlan.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = WealthPlan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFF62BD76C360098FDAF /* APILayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = APILayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFF72BD76C360098FDAF /* AppSetting.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = AppSetting.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFF82BD76C360098FDAF /* Authentication.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Authentication.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFF92BD76C360098FDAF /* CUIModule.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = CUIModule.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFA2BD76C360098FDAF /* DGCharts.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = DGCharts.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFB2BD76C360098FDAF /* History.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = History.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFC2BD76C360098FDAF /* Home.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Home.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFD2BD76C360098FDAF /* InstrumentSearch.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = InstrumentSearch.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFE2BD76C360098FDAF /* Login.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Login.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0ADFFF2BD76C360098FDAF /* Market.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Market.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0002BD76C360098FDAF /* MarketDashboard.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = MarketDashboard.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0012BD76C360098FDAF /* Notification.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Notification.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0022BD76C360098FDAF /* Onboarding.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Onboarding.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0032BD76C360098FDAF /* ReportGeneration.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = ReportGeneration.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0042BD76C360098FDAF /* SharedData.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SharedData.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0052BD76C360098FDAF /* Storage.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Storage.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0062BD76C360098FDAF /* Wallet.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = Wallet.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0072BD76C360098FDAF /* WealthPlan.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = WealthPlan.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		DF0AE0F42BD7753B0098FDAF /* AppCoordinator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		DF0AE0F52BD7753B0098FDAF /* MainTabbarController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabbarController.swift; sourceTree = "<group>"; };
		DF0AE0F62BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "MainTabbarCoordinator+CustomTheme.swift"; sourceTree = "<group>"; };
		DF0AE0F72BD7753B0098FDAF /* MainTabbarCoordinator.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainTabbarCoordinator.swift; sourceTree = "<group>"; };
		DF0AE0F92BD7753B0098FDAF /* SplashScreenViewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SplashScreenViewModel.swift; sourceTree = "<group>"; };
		DF0AE0FA2BD7753B0098FDAF /* SplashScreenViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SplashScreenViewController.swift; sourceTree = "<group>"; };
		DF273C762BD75E7C003F9464 /* Merit-DEV.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Merit-DEV.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273C792BD75E7C003F9464 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		DF273C822BD75E7D003F9464 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DF273C852BD75E7D003F9464 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DF273C872BD75E7D003F9464 /* Merit-DEV.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Merit-DEV.plist"; sourceTree = "<group>"; };
		DF273CB12BD76258003F9464 /* Merit-UAT.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Merit-UAT.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273CB22BD76258003F9464 /* Merit-UAT.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Merit-UAT.plist"; path = "/Users/<USER>/Desktop/desk/merit/Merit/Merit-UAT.plist"; sourceTree = "<absolute>"; };
		DF273CC32BD76259003F9464 /* Merit-PROD.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Merit-PROD.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DF273CC42BD76259003F9464 /* Merit-PROD.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Merit-PROD.plist"; path = "/Users/<USER>/Desktop/desk/merit/Merit/Merit-PROD.plist"; sourceTree = "<absolute>"; };
		DF273CC52BD76441003F9464 /* main.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = main.swift; sourceTree = "<group>"; };
		DF273CC62BD76441003F9464 /* MainUIApplication.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MainUIApplication.swift; sourceTree = "<group>"; };
		DF93F0892FAC9A91A0ECF390 /* Pods-Merit-UAT.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Merit-UAT.release.xcconfig"; path = "Target Support Files/Pods-Merit-UAT/Pods-Merit-UAT.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		DF273C732BD75E7C003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0ADFA22BD76BF40098FDAF /* DGCharts.framework in Frameworks */,
				DF0ADFB62BD76BF50098FDAF /* SharedData.framework in Frameworks */,
				DF0ADFB22BD76BF50098FDAF /* Onboarding.framework in Frameworks */,
				DF0ADFBA2BD76BF50098FDAF /* Wallet.framework in Frameworks */,
				DF0ADFB42BD76BF50098FDAF /* ReportGeneration.framework in Frameworks */,
				DF0ADF9E2BD76BF40098FDAF /* Authentication.framework in Frameworks */,
				DF0ADFA42BD76BF40098FDAF /* History.framework in Frameworks */,
				DF0ADFA62BD76BF50098FDAF /* Home.framework in Frameworks */,
				DF0ADFB82BD76BF50098FDAF /* Storage.framework in Frameworks */,
				DF0ADFA82BD76BF50098FDAF /* InstrumentSearch.framework in Frameworks */,
				DF0ADFAE2BD76BF50098FDAF /* MarketDashboard.framework in Frameworks */,
				FCC41598AB395FC9796B50DF /* Pods_Merit_DEV.framework in Frameworks */,
				DF0ADFBC2BD76BF50098FDAF /* WealthPlan.framework in Frameworks */,
				DF0ADF9A2BD76BF40098FDAF /* APILayer.framework in Frameworks */,
				DF0ADFA02BD76BF40098FDAF /* CUIModule.framework in Frameworks */,
				DF0ADFAA2BD76BF50098FDAF /* Login.framework in Frameworks */,
				DF0ADFB02BD76BF50098FDAF /* Notification.framework in Frameworks */,
				DF0ADF9C2BD76BF40098FDAF /* AppSetting.framework in Frameworks */,
				DF0ADFAC2BD76BF50098FDAF /* Market.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CA72BD76258003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0ADFD92BD76C1A0098FDAF /* DGCharts.framework in Frameworks */,
				DF0ADFED2BD76C1B0098FDAF /* SharedData.framework in Frameworks */,
				DF0ADFE92BD76C1B0098FDAF /* Onboarding.framework in Frameworks */,
				DF0ADFF12BD76C1C0098FDAF /* Wallet.framework in Frameworks */,
				DF0ADFEB2BD76C1B0098FDAF /* ReportGeneration.framework in Frameworks */,
				DF0ADFD52BD76C1A0098FDAF /* Authentication.framework in Frameworks */,
				DF0ADFDB2BD76C1B0098FDAF /* History.framework in Frameworks */,
				DF0ADFDD2BD76C1B0098FDAF /* Home.framework in Frameworks */,
				DF0ADFEF2BD76C1C0098FDAF /* Storage.framework in Frameworks */,
				DF0ADFDF2BD76C1B0098FDAF /* InstrumentSearch.framework in Frameworks */,
				DF0ADFE52BD76C1B0098FDAF /* MarketDashboard.framework in Frameworks */,
				F7DEA0AAC9F4EBFB30353F0E /* Pods_Merit_UAT.framework in Frameworks */,
				DF0ADFF32BD76C1C0098FDAF /* WealthPlan.framework in Frameworks */,
				DF0ADFD12BD76C1A0098FDAF /* APILayer.framework in Frameworks */,
				DF0ADFD72BD76C1A0098FDAF /* CUIModule.framework in Frameworks */,
				DF0ADFE12BD76C1B0098FDAF /* Login.framework in Frameworks */,
				DF0ADFE72BD76C1B0098FDAF /* Notification.framework in Frameworks */,
				DF0ADFD32BD76C1A0098FDAF /* AppSetting.framework in Frameworks */,
				DF0ADFE32BD76C1B0098FDAF /* Market.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CB92BD76259003F9464 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0AE0102BD76C370098FDAF /* DGCharts.framework in Frameworks */,
				DF0AE0242BD76C380098FDAF /* SharedData.framework in Frameworks */,
				DF0AE0202BD76C380098FDAF /* Onboarding.framework in Frameworks */,
				DF0AE0282BD76C380098FDAF /* Wallet.framework in Frameworks */,
				DF0AE0222BD76C380098FDAF /* ReportGeneration.framework in Frameworks */,
				DF0AE00C2BD76C370098FDAF /* Authentication.framework in Frameworks */,
				DF0AE0122BD76C370098FDAF /* History.framework in Frameworks */,
				DF0AE0142BD76C370098FDAF /* Home.framework in Frameworks */,
				DF0AE0262BD76C380098FDAF /* Storage.framework in Frameworks */,
				DF0AE0162BD76C370098FDAF /* InstrumentSearch.framework in Frameworks */,
				DF0AE01C2BD76C370098FDAF /* MarketDashboard.framework in Frameworks */,
				0E2A8C9C77623B37DC3828CE /* Pods_Merit_PROD.framework in Frameworks */,
				DF0AE02A2BD76C380098FDAF /* WealthPlan.framework in Frameworks */,
				DF0AE0082BD76C360098FDAF /* APILayer.framework in Frameworks */,
				DF0AE00E2BD76C370098FDAF /* CUIModule.framework in Frameworks */,
				DF0AE0182BD76C370098FDAF /* Login.framework in Frameworks */,
				DF0AE01E2BD76C380098FDAF /* Notification.framework in Frameworks */,
				DF0AE00A2BD76C360098FDAF /* AppSetting.framework in Frameworks */,
				DF0AE01A2BD76C370098FDAF /* Market.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		82D331F5FB4F5CB8C2CFA724 /* Pods */ = {
			isa = PBXGroup;
			children = (
				C02BCA1538DC4B0AB8B93638 /* Pods-Merit.debug.xcconfig */,
				1E3E2A0BBFFD04D540C0AF21 /* Pods-Merit.release.xcconfig */,
				577942E4C63E62C67CD9E7B8 /* Pods-Merit-DEV.debug.xcconfig */,
				3CC78B75BDCF86BA8B14A90A /* Pods-Merit-DEV.release.xcconfig */,
				31305F2255B77B5178846F78 /* Pods-Merit-PROD.debug.xcconfig */,
				C8F2E421371403591197C16A /* Pods-Merit-PROD.release.xcconfig */,
				0315DFF076625A82CB799C5C /* Pods-Merit-UAT.debug.xcconfig */,
				DF93F0892FAC9A91A0ECF390 /* Pods-Merit-UAT.release.xcconfig */,
			);
			name = Pods;
			path = ../Pods;
			sourceTree = "<group>";
		};
		87B758A787A57E51F1BA1E13 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DF0ADFF62BD76C360098FDAF /* APILayer.framework */,
				DF0ADFF72BD76C360098FDAF /* AppSetting.framework */,
				DF0ADFF82BD76C360098FDAF /* Authentication.framework */,
				DF0ADFF92BD76C360098FDAF /* CUIModule.framework */,
				DF0ADFFA2BD76C360098FDAF /* DGCharts.framework */,
				DF0ADFFB2BD76C360098FDAF /* History.framework */,
				DF0ADFFC2BD76C360098FDAF /* Home.framework */,
				DF0ADFFD2BD76C360098FDAF /* InstrumentSearch.framework */,
				DF0ADFFE2BD76C360098FDAF /* Login.framework */,
				DF0ADFFF2BD76C360098FDAF /* Market.framework */,
				DF0AE0002BD76C360098FDAF /* MarketDashboard.framework */,
				DF0AE0012BD76C360098FDAF /* Notification.framework */,
				DF0AE0022BD76C360098FDAF /* Onboarding.framework */,
				DF0AE0032BD76C360098FDAF /* ReportGeneration.framework */,
				DF0AE0042BD76C360098FDAF /* SharedData.framework */,
				DF0AE0052BD76C360098FDAF /* Storage.framework */,
				DF0AE0062BD76C360098FDAF /* Wallet.framework */,
				DF0AE0072BD76C360098FDAF /* WealthPlan.framework */,
				DF0ADFBF2BD76C1A0098FDAF /* APILayer.framework */,
				DF0ADFC02BD76C1A0098FDAF /* AppSetting.framework */,
				DF0ADFC12BD76C1A0098FDAF /* Authentication.framework */,
				DF0ADFC22BD76C1A0098FDAF /* CUIModule.framework */,
				DF0ADFC32BD76C1A0098FDAF /* DGCharts.framework */,
				DF0ADFC42BD76C1A0098FDAF /* History.framework */,
				DF0ADFC52BD76C1A0098FDAF /* Home.framework */,
				DF0ADFC62BD76C1A0098FDAF /* InstrumentSearch.framework */,
				DF0ADFC72BD76C1A0098FDAF /* Login.framework */,
				DF0ADFC82BD76C1A0098FDAF /* Market.framework */,
				DF0ADFC92BD76C1A0098FDAF /* MarketDashboard.framework */,
				DF0ADFCA2BD76C1A0098FDAF /* Notification.framework */,
				DF0ADFCB2BD76C1A0098FDAF /* Onboarding.framework */,
				DF0ADFCC2BD76C1A0098FDAF /* ReportGeneration.framework */,
				DF0ADFCD2BD76C1A0098FDAF /* SharedData.framework */,
				DF0ADFCE2BD76C1A0098FDAF /* Storage.framework */,
				DF0ADFCF2BD76C1A0098FDAF /* Wallet.framework */,
				DF0ADFD02BD76C1A0098FDAF /* WealthPlan.framework */,
				DF0ADF882BD76BF40098FDAF /* APILayer.framework */,
				DF0ADF892BD76BF40098FDAF /* AppSetting.framework */,
				DF0ADF8A2BD76BF40098FDAF /* Authentication.framework */,
				DF0ADF8B2BD76BF40098FDAF /* CUIModule.framework */,
				DF0ADF8C2BD76BF40098FDAF /* DGCharts.framework */,
				DF0ADF8D2BD76BF40098FDAF /* History.framework */,
				DF0ADF8E2BD76BF40098FDAF /* Home.framework */,
				DF0ADF8F2BD76BF40098FDAF /* InstrumentSearch.framework */,
				DF0ADF902BD76BF40098FDAF /* Login.framework */,
				DF0ADF912BD76BF40098FDAF /* Market.framework */,
				DF0ADF922BD76BF40098FDAF /* MarketDashboard.framework */,
				DF0ADF932BD76BF40098FDAF /* Notification.framework */,
				DF0ADF942BD76BF40098FDAF /* Onboarding.framework */,
				DF0ADF952BD76BF40098FDAF /* ReportGeneration.framework */,
				DF0ADF962BD76BF40098FDAF /* SharedData.framework */,
				DF0ADF972BD76BF40098FDAF /* Storage.framework */,
				DF0ADF982BD76BF40098FDAF /* Wallet.framework */,
				DF0ADF992BD76BF40098FDAF /* WealthPlan.framework */,
				3926C4470D2252AE05FE2859 /* Pods_Merit_DEV.framework */,
				1FACA542BF6C4EF7D0177D74 /* Pods_Merit_PROD.framework */,
				3C33F350B8969484CD6AF5B4 /* Pods_Merit_UAT.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DF0AE0F22BD774FE0098FDAF /* Delegate */ = {
			isa = PBXGroup;
			children = (
				DF273C792BD75E7C003F9464 /* AppDelegate.swift */,
				83D977A82CEF2B0A00A6BC4D /* Merit-DEV-Bridging-Header.h */,
				83D977A92CEF2B0A00A6BC4D /* Merit-UAT-Bridging-Header.h */,
				83D977AA2CEF2B0A00A6BC4D /* Merit-PROD-Bridging-Header.h */,
			);
			path = Delegate;
			sourceTree = "<group>";
		};
		DF0AE0F32BD7753B0098FDAF /* Coordinator */ = {
			isa = PBXGroup;
			children = (
				DF0AE0F42BD7753B0098FDAF /* AppCoordinator.swift */,
				DF0AE0F72BD7753B0098FDAF /* MainTabbarCoordinator.swift */,
				DF0AE0F52BD7753B0098FDAF /* MainTabbarController.swift */,
				DF0AE0F62BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift */,
			);
			path = Coordinator;
			sourceTree = "<group>";
		};
		DF0AE0F82BD7753B0098FDAF /* SplashScreen */ = {
			isa = PBXGroup;
			children = (
				DF0AE0FA2BD7753B0098FDAF /* SplashScreenViewController.swift */,
				DF0AE0F92BD7753B0098FDAF /* SplashScreenViewModel.swift */,
			);
			path = SplashScreen;
			sourceTree = "<group>";
		};
		DF273C6D2BD75E7C003F9464 = {
			isa = PBXGroup;
			children = (
				83D977A72CEED7EA00A6BC4D /* Merit-DEV.entitlements */,
				83D977A62CEED7E200A6BC4D /* Merit-PROD.entitlements */,
				83D977A52CEED7B900A6BC4D /* Merit-UAT.entitlements */,
				DF273C782BD75E7C003F9464 /* Merit */,
				DF273C772BD75E7C003F9464 /* Products */,
				82D331F5FB4F5CB8C2CFA724 /* Pods */,
				87B758A787A57E51F1BA1E13 /* Frameworks */,
				DF273CB22BD76258003F9464 /* Merit-UAT.plist */,
				DF273C872BD75E7D003F9464 /* Merit-DEV.plist */,
				DF273CC42BD76259003F9464 /* Merit-PROD.plist */,
			);
			sourceTree = "<group>";
		};
		DF273C772BD75E7C003F9464 /* Products */ = {
			isa = PBXGroup;
			children = (
				DF273C762BD75E7C003F9464 /* Merit-DEV.app */,
				DF273CB12BD76258003F9464 /* Merit-UAT.app */,
				DF273CC32BD76259003F9464 /* Merit-PROD.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DF273C782BD75E7C003F9464 /* Merit */ = {
			isa = PBXGroup;
			children = (
				DF273CC52BD76441003F9464 /* main.swift */,
				DF273CC62BD76441003F9464 /* MainUIApplication.swift */,
				DF273C822BD75E7D003F9464 /* Assets.xcassets */,
				DF0AE0F32BD7753B0098FDAF /* Coordinator */,
				DF0AE0F22BD774FE0098FDAF /* Delegate */,
				DF273C842BD75E7D003F9464 /* LaunchScreen.storyboard */,
				DF0AE0F82BD7753B0098FDAF /* SplashScreen */,
				83A64E4C2D39570900E9F3BE /* InfoPlist.strings */,
			);
			path = Merit;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DF273C752BD75E7C003F9464 /* Merit-DEV */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273C8A2BD75E7D003F9464 /* Build configuration list for PBXNativeTarget "Merit-DEV" */;
			buildPhases = (
				C6659E47F8EAD26805B50443 /* [CP] Check Pods Manifest.lock */,
				DF273C722BD75E7C003F9464 /* Sources */,
				DF273C732BD75E7C003F9464 /* Frameworks */,
				DF273C742BD75E7C003F9464 /* Resources */,
				A412CF304221BA161AC54BE2 /* [CP] Embed Pods Frameworks */,
				DF0ADFBE2BD76BF60098FDAF /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Merit-DEV";
			productName = Merit;
			productReference = DF273C762BD75E7C003F9464 /* Merit-DEV.app */;
			productType = "com.apple.product-type.application";
		};
		DF273CA12BD76258003F9464 /* Merit-UAT */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273CAE2BD76258003F9464 /* Build configuration list for PBXNativeTarget "Merit-UAT" */;
			buildPhases = (
				DF273CA22BD76258003F9464 /* [CP] Check Pods Manifest.lock */,
				DF273CA32BD76258003F9464 /* Sources */,
				DF273CA72BD76258003F9464 /* Frameworks */,
				DF273CA92BD76258003F9464 /* Resources */,
				DF273CAD2BD76258003F9464 /* [CP] Embed Pods Frameworks */,
				DF0ADFF52BD76C1C0098FDAF /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Merit-UAT";
			productName = Merit;
			productReference = DF273CB12BD76258003F9464 /* Merit-UAT.app */;
			productType = "com.apple.product-type.application";
		};
		DF273CB32BD76259003F9464 /* Merit-PROD */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DF273CC02BD76259003F9464 /* Build configuration list for PBXNativeTarget "Merit-PROD" */;
			buildPhases = (
				DF273CB42BD76259003F9464 /* [CP] Check Pods Manifest.lock */,
				DF273CB52BD76259003F9464 /* Sources */,
				DF273CB92BD76259003F9464 /* Frameworks */,
				DF273CBB2BD76259003F9464 /* Resources */,
				DF273CBF2BD76259003F9464 /* [CP] Embed Pods Frameworks */,
				DF0AE02C2BD76C390098FDAF /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Merit-PROD";
			productName = Merit;
			productReference = DF273CC32BD76259003F9464 /* Merit-PROD.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DF273C6E2BD75E7C003F9464 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					DF273C752BD75E7C003F9464 = {
						CreatedOnToolsVersion = 15.0;
						LastSwiftMigration = 1540;
					};
					DF273CA12BD76258003F9464 = {
						LastSwiftMigration = 1540;
					};
					DF273CB32BD76259003F9464 = {
						LastSwiftMigration = 1540;
					};
				};
			};
			buildConfigurationList = DF273C712BD75E7C003F9464 /* Build configuration list for PBXProject "Merit" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-HK",
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = DF273C6D2BD75E7C003F9464;
			productRefGroup = DF273C772BD75E7C003F9464 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DF273C752BD75E7C003F9464 /* Merit-DEV */,
				DF273CA12BD76258003F9464 /* Merit-UAT */,
				DF273CB32BD76259003F9464 /* Merit-PROD */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DF273C742BD75E7C003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF273C862BD75E7D003F9464 /* LaunchScreen.storyboard in Resources */,
				83A64E4E2D39570900E9F3BE /* InfoPlist.strings in Resources */,
				DF273C832BD75E7D003F9464 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CA92BD76258003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF273CAA2BD76258003F9464 /* LaunchScreen.storyboard in Resources */,
				83A64E4F2D39570900E9F3BE /* InfoPlist.strings in Resources */,
				DF273CAB2BD76258003F9464 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CBB2BD76259003F9464 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF273CBC2BD76259003F9464 /* LaunchScreen.storyboard in Resources */,
				83A64E502D39570900E9F3BE /* InfoPlist.strings in Resources */,
				DF273CBD2BD76259003F9464 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A412CF304221BA161AC54BE2 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-DEV/Pods-Merit-DEV-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-DEV/Pods-Merit-DEV-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Merit-DEV/Pods-Merit-DEV-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C6659E47F8EAD26805B50443 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Merit-DEV-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DF273CA22BD76258003F9464 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Merit-UAT-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DF273CAD2BD76258003F9464 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-UAT/Pods-Merit-UAT-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-UAT/Pods-Merit-UAT-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Merit-UAT/Pods-Merit-UAT-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DF273CB42BD76259003F9464 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Merit-PROD-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DF273CBF2BD76259003F9464 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-PROD/Pods-Merit-PROD-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Merit-PROD/Pods-Merit-PROD-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Merit-PROD/Pods-Merit-PROD-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DF273C722BD75E7C003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0AE1012BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */,
				DF0AE0FB2BD7753B0098FDAF /* AppCoordinator.swift in Sources */,
				DF0AE10A2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */,
				DF273CCA2BD76441003F9464 /* MainUIApplication.swift in Sources */,
				DF0AE0FE2BD7753B0098FDAF /* MainTabbarController.swift in Sources */,
				DF0AE1042BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */,
				DF0AE1072BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */,
				DF273C7A2BD75E7C003F9464 /* AppDelegate.swift in Sources */,
				DF273CC72BD76441003F9464 /* main.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CA32BD76258003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0AE1022BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */,
				DF0AE0FC2BD7753B0098FDAF /* AppCoordinator.swift in Sources */,
				DF0AE10B2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */,
				DF273CCB2BD76441003F9464 /* MainUIApplication.swift in Sources */,
				DF0AE0FF2BD7753B0098FDAF /* MainTabbarController.swift in Sources */,
				DF0AE1052BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */,
				DF0AE1082BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */,
				DF273CA52BD76258003F9464 /* AppDelegate.swift in Sources */,
				DF273CC82BD76441003F9464 /* main.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DF273CB52BD76259003F9464 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DF0AE1032BD7753B0098FDAF /* MainTabbarCoordinator+CustomTheme.swift in Sources */,
				DF0AE0FD2BD7753B0098FDAF /* AppCoordinator.swift in Sources */,
				DF0AE10C2BD7753B0098FDAF /* SplashScreenViewController.swift in Sources */,
				DF273CCC2BD76441003F9464 /* MainUIApplication.swift in Sources */,
				DF0AE1002BD7753B0098FDAF /* MainTabbarController.swift in Sources */,
				DF0AE1062BD7753B0098FDAF /* MainTabbarCoordinator.swift in Sources */,
				DF0AE1092BD7753B0098FDAF /* SplashScreenViewModel.swift in Sources */,
				DF273CB72BD76259003F9464 /* AppDelegate.swift in Sources */,
				DF273CC92BD76441003F9464 /* main.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		83A64E4C2D39570900E9F3BE /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				83A64E4D2D39570900E9F3BE /* en */,
				83A64E512D39571800E9F3BE /* zh-HK */,
				83A64E522D39669400E9F3BE /* zh-Hans */,
				83A64E532D39669B00E9F3BE /* zh-Hant */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		DF273C842BD75E7D003F9464 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DF273C852BD75E7D003F9464 /* Base */,
				83443CA32D022ACD00BDC625 /* zh-HK */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		DF273C882BD75E7D003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DF273C892BD75E7D003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DF273C8B2BD75E7D003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 577942E4C63E62C67CD9E7B8 /* Pods-Merit-DEV.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-DEV.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 10;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X26R27Y4YJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-DEV.plist";
				INFOPLIST_KEY_CFBundleDisplayName = MeritAM;
				INFOPLIST_KEY_NSCameraUsageDescription = "Please allow to access camera.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEV";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.merit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = merit_development;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-DEV-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DF273C8C2BD75E7D003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3CC78B75BDCF86BA8B14A90A /* Pods-Merit-DEV.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-DEV.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 10;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = X26R27Y4YJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-DEV.plist";
				INFOPLIST_KEY_CFBundleDisplayName = MeritAM;
				INFOPLIST_KEY_NSCameraUsageDescription = "Please allow to access camera.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DDEV";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.merit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = merit_development;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-DEV-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DF273CAF2BD76258003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0315DFF076625A82CB799C5C /* Pods-Merit-UAT.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-UAT.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 60;
				DEVELOPMENT_TEAM = X26R27Y4YJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-UAT.plist";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires camera access to scan and upload documents securely and to perform liveness authentication for identity verification.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.2;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DUAT";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.merit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-UAT-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DF273CB02BD76258003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DF93F0892FAC9A91A0ECF390 /* Pods-Merit-UAT.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-UAT.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 60;
				DEVELOPMENT_TEAM = X26R27Y4YJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-UAT.plist";
				INFOPLIST_KEY_LSSupportsOpeningDocumentsInPlace = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires camera access to scan and upload documents securely and to perform liveness authentication for identity verification.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.2;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DUAT";
				PRODUCT_BUNDLE_IDENTIFIER = com.sirius.merit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-UAT-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DF273CC12BD76259003F9464 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 31305F2255B77B5178846F78 /* Pods-Merit-PROD.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-PROD.entitlements";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 115;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3TG24W5M35;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-PROD.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Merit;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires camera access to scan and upload documents securely and to perform liveness authentication for identity verification.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.10;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DPROD";
				PRODUCT_BUNDLE_IDENTIFIER = "com.merit-am.merit";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Merit Prod";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-PROD-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DF273CC22BD76259003F9464 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C8F2E421371403591197C16A /* Pods-Merit-PROD.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Merit-PROD.entitlements";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 115;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 3TG24W5M35;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Merit-PROD.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Merit;
				INFOPLIST_KEY_NSCameraUsageDescription = "This app requires camera access to scan and upload documents securely and to perform liveness authentication for identity verification.";
				INFOPLIST_KEY_NSFaceIDUsageDescription = "Scan to login, or cancel to use PIN.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Please allow to use photo library.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.10;
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS -DPROD";
				PRODUCT_BUNDLE_IDENTIFIER = "com.merit-am.merit";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Merit Prod";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Merit/Delegate/Merit-PROD-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DF273C712BD75E7C003F9464 /* Build configuration list for PBXProject "Merit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273C882BD75E7D003F9464 /* Debug */,
				DF273C892BD75E7D003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273C8A2BD75E7D003F9464 /* Build configuration list for PBXNativeTarget "Merit-DEV" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273C8B2BD75E7D003F9464 /* Debug */,
				DF273C8C2BD75E7D003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273CAE2BD76258003F9464 /* Build configuration list for PBXNativeTarget "Merit-UAT" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273CAF2BD76258003F9464 /* Debug */,
				DF273CB02BD76258003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DF273CC02BD76259003F9464 /* Build configuration list for PBXNativeTarget "Merit-PROD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DF273CC12BD76259003F9464 /* Debug */,
				DF273CC22BD76259003F9464 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DF273C6E2BD75E7C003F9464 /* Project object */;
}
