//
//  AppDelegate.swift
//  Merit
//
//  Created by <PERSON><PERSON> on 4/23/67.
//
import CUIModule
import APILayer
import Storage
import IQKeyboardManagerSwift
import RxSwift
import UIKit
import Kingfisher
//import Wormholy
import SharedData

class AppDelegate: UIResponder, UIApplicationDelegate {
    private let disposeBag = DisposeBag()
    
    private lazy var mainWindow = UIWindow()
    private let router = AppCoordinator().strongRouter
    
    private var orientationLock = ScreenOrientation.defaultOrientation
    
    static var jpushAppKey: String = ""
    static var jpushForProd: Bool = false

    func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        // Override point for customization after application launch.
        configureAppStartService()
        mainWindow.set(AppTheme.init(rawValue: LocalPreference.appTheme ?? AppTheme.default.rawValue) ?? .default)
        configureURLEnvironment()
        observeSessionTimeout()
        setUpPushNotification(launchOptions: launchOptions)
        router.setRoot(for: mainWindow)
        
        CustomThemeManager.shared.saveTheme(
            CustomThemeModel(
                name: "", primaryId: ThemeColor.presetPrimary1.rawValue,
                secondaryId: ThemeColor.presetSecondary1.rawValue,
                thirdId: ThemeColor.presetThird1.rawValue,
                textHighLightId: ThemeColor.presetTextHighLight1.rawValue,
                isPreset: true, isEnable: true
            )
        )
        
        ScreenOrientation.setOrientation
            .subscribe(onNext: { [unowned self] in
                orientationLock = $0
                
                if orientationLock == .landscapeRight {
                    UIDevice.current.setValue(UIInterfaceOrientation.landscapeRight.rawValue, forKey: "orientation")
                }
            }).disposed(by: disposeBag)

        return true
    }
    
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        orientationLock
    }

    func application(
        _ application: UIApplication,
        shouldAllowExtensionPointIdentifier extensionPointIdentifier: UIApplication.ExtensionPointIdentifier
    ) -> Bool {
        return extensionPointIdentifier != UIApplication.ExtensionPointIdentifier.keyboard
    }
    
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        JPUSHService.registerDeviceToken(deviceToken)
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: any Error) {
        print("Did failed to register For Remote Notifications With Error: \(error.localizedDescription)")
    }
    
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        JPUSHService.handleRemoteNotification(userInfo)
        completionHandler(.newData)
    }
}

// MARK: - App Configuration
extension AppDelegate {

    func configureAppStartService() {
        configureAppTarget()
        UIFont.loadAllFonts()
        configureKeyboard()
        setImageCachePolicy()
        setDeviceID()
    }

    func configureAppTarget() {
    #if DEV
    LocalPreference.appTarget = AppTarget.dev.rawValue
//    Wormholy.shakeEnabled = true
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = false
    #elseif UAT
    LocalPreference.appTarget = AppTarget.uat.rawValue
//    Wormholy.shakeEnabled = true
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = false
    #elseif PROD
    LocalPreference.appTarget = AppTarget.prod.rawValue
//    Wormholy.shakeEnabled = false
    AppDelegate.jpushAppKey = "d733ba82d878ba46bc9ebf5a"
    AppDelegate.jpushForProd = true
    #else
    LocalPreference.appTarget = AppTarget.uat.rawValue
//    Wormholy.shakeEnabled = false
    AppDelegate.jpushForProd = false
    #endif
    }

    func configureKeyboard() {
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.shouldResignOnTouchOutside = true
        IQKeyboardManager.shared.shouldShowToolbarPlaceholder = false
    }

    func configureURLEnvironment() {
        switch AppTarget.get(LocalPreference.appTarget) {
        case .dev:
            APIConstants.shared.configure(environment: .dev)
            WSConfiguration.config(env: .dev)

        case .uat:
            APIConstants.shared.configure(environment: .uat)
            WSConfiguration.config(env: .uat)

        case .prod:
            APIConstants.shared.configure(environment: .prod)
            WSConfiguration.config(env: .prod)
        }

        // Set custom URL for address info endpoint
        let addressEnvironment = URLEnvironment(url: "https://aig-wallet-sit.siriustech.io",
                                                name: "Address Info",
                                                version: "0.0.1",
                                                enableSSLPinning: true,
                                                allowInsecureConnection: true)

        APIConstants.shared.registerCustomUrlFor(endpoint: AddressInfoEndPoint.service,
                                                 url: addressEnvironment)
        // Set custom URL for address info endpoint
        let newsEnvironment = URLEnvironment(url: "https://api.marketaux.com",
                                             name: "News",
                                             version: "0.0.1",
                                             enableSSLPinning: true,
                                             allowInsecureConnection: true)

        APIConstants.shared.registerCustomUrlFor(endpoint: GetMarketNewsEndPoint.service,
                                                 url: newsEnvironment)
    }
    
    func setImageCachePolicy() {
        ImageCache.default.memoryStorage.config.expiration = .days(7)
    }
    
    func setDeviceID() {
        if LocalPreference.deviceId == nil {
            LocalPreference.deviceId = UIDevice.current.identifierForVendor?.uuidString
        }
        
        LocalPreference.appVersion = "\(Bundle.main.releaseVersionNumber ?? "").\(Bundle.main.buildVersionNumber ?? "")"
    }
    
    func observeSessionTimeout() {
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(self.sessionTimeout(_:)),
                                               name: .sessionTimeout,
                                               object: nil)
    }
    
    @objc func sessionTimeout(_ notification: Notification) {
        router.trigger(.logout(isSessionExpired: true))
    }
    
    func setUpPushNotification(launchOptions: [UIApplication.LaunchOptionsKey: Any]?) {
        // check push noti
        SystemAuthorityHandler.checkPushNotiAuth(handler: { _ in
            // can do more if not authroize
            // eg: like show alert to allow noti in setting
        })
        UNUserNotificationCenter.current().delegate = self
        
        let entity = JPUSHRegisterEntity()
        entity.types = NSInteger(UNAuthorizationOptions.alert.rawValue) |
        NSInteger(UNAuthorizationOptions.sound.rawValue) |
        NSInteger(UNAuthorizationOptions.badge.rawValue) |
        NSInteger(UNAuthorizationOptions.provisional.rawValue)
        
        JPUSHService.register(forRemoteNotificationConfig: entity, delegate: self)
        
        JPUSHService.registrationIDCompletionHandler { _, registrationID in
            Keychain.jPushRegistrationID = registrationID
        }
        
        JPUSHService.setup(withOption: launchOptions,
                           appKey: AppDelegate.jpushAppKey,
                           channel: "",
                           apsForProduction: AppDelegate.jpushForProd)
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension AppDelegate: UNUserNotificationCenterDelegate {

    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        if #available(iOS 14.0, *) {
            completionHandler([.sound, .list, .banner])
        } else {
            completionHandler([.sound, .alert])
        }
    }
}

// MARK: - JPUSHRegisterDelegate
extension AppDelegate: JPUSHRegisterDelegate {
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (Int) -> Void) {
        let userInfo = notification.request.content.userInfo
        let request = notification.request // 收到推送的请求
        //        let content = request.content // 收到推送的消息内容
        
        //        let badge = content.badge // 推送消息的角标
        //        let body = content.body   // 推送消息体
        //        let sound = content.sound // 推送消息的声音
        //        let subtitle = content.subtitle // 推送消息的副标题
        //        let title = content.title // 推送消息的标题
        
        if (notification.request.trigger?.isKind(of: UNPushNotificationTrigger.self) == true) {
            // 注意调用
            JPUSHService.handleRemoteNotification(userInfo)
        } else {
            
        }
        
        completionHandler(Int(UNNotificationPresentationOptions.badge.rawValue |
                              UNNotificationPresentationOptions.sound.rawValue |
                              UNNotificationPresentationOptions.alert.rawValue))
    }
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        let userInfo = response.notification.request.content.userInfo
        let request = response.notification.request // 收到推送的请求
        //        let content = request.content // 收到推送的消息内容
        
        //        let badge = content.badge // 推送消息的角标
        //        let body = content.body   // 推送消息体
        //        let sound = content.sound // 推送消息的声音
        //        let subtitle = content.subtitle // 推送消息的副标题
        //        let title = content.title // 推送消息的标题
        
        if (response.notification.request.trigger?.isKind(of: UNPushNotificationTrigger.self) == true) {
            // 注意调用
            JPUSHService.handleRemoteNotification(userInfo)
            
        } else {
            
        }
        
        completionHandler()
    }
    
    func jpushNotificationCenter(_ center: UNUserNotificationCenter, openSettingsFor notification: UNNotification) {
        
    }
    
    func jpushNotificationAuthorization(_ status: JPAuthorizationStatus, withInfo info: [AnyHashable : Any]?) {
        
    }
}
