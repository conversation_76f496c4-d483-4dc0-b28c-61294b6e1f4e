//
//  WalletSummaryCell.swift
//  Home
//
//  Created by <PERSON> on 20/02/2024.
//

import UIKit
import Core
import CUIModule
import SharedData
import RxSwift

final class WalletSummaryCell: UICollectionViewCell, AnyView, LocalizeNeededView {

    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 12)
        stackView.addArrangedSubviews([CUIDivider(canvasHeight: 8),
                                       totalBalanceStackView,
                                       distributionBarView,
                                       CUIDivider(canvasHeight: 8)])

        return stackView
    }()

    private lazy var totalBalanceStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([titleLabel,
                                       balanceStackView,
                                       gainLossStackView])

        return stackView
    }()

    private lazy var currencyWithEyeStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 6)
        stackView.addArrangedSubviews([currencyLabel,
                                       privacyToggleButton])

        return stackView
    }()

    private lazy var titleLabel = UILabel(font: Font.regular.of(size: 12),
                                          text: "key0615".localized(),
                                          textColor: Color.txtDisabled)

    private lazy var privacyToggleButton = {
        let button = UIButton()
        button.setImage(.image(named: "merit_eye"), for: .normal)
        button.setImage(.image(named: "merit_eye_slash"), for: .selected)
        button.snp.makeConstraints { make in
            make.width.height.equalTo(16)
        }

        // Accessibility support
        button.accessibilityLabel = "Toggle portfolio privacy"
        button.accessibilityHint = "Tap to show or hide financial data"

        return button
    }()
    private lazy var balanceStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)

        stackView.addArrangedSubviews([totalBalanceLabel,
                                       currencyWithEyeStackView])

        return stackView
    }()
    
    private lazy var totalBalanceLabel = UILabel(font: Font.semiBold.of(size: 24),
                                                 textColor: Color.txtTitle)
    
    private lazy var currencyLabel = UILabel(font: Font.regular.of(size: 12),
                                             textColor: Color.txtInactive)
    
    private lazy var gainLossStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)
        stackView.addArrangedSubviews([unrealizedGLView,
                                       unrealizedGLPercentView])
        
        return stackView
    }()
    
    private lazy var unrealizedGLView = PriceChangeView()
    private lazy var unrealizedGLPercentView = PercentChangeBadge()
    
    private lazy var distributionBarView = DistributionBarView()

    // MARK: Properties
    private var currentDisplayModel: DisplayModel?
    private let disposeBag = DisposeBag()

    // MARK: Life cycle
    override init(frame: CGRect) {
        super.init(frame: frame)

        setupUI()
        bindActions()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()

        // Reset display model when cell is reused
        currentDisplayModel = nil

        setTexts()
    }
    
    func setupUI() {
        contentView.addSubview(contentStackView)

        contentStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    func bindActions() {
        // Bind privacy toggle button tap
        privacyToggleButton.addTarget(self, action: #selector(privacyToggleButtonTapped), for: .touchUpInside)

        // Set initial state
        updatePrivacyButtonState()

        // Listen for privacy state changes from other screens
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(privacyStateChanged),
            name: NSNotification.Name("PrivacyModeChanged"),
            object: nil
        )
    }

    @objc private func privacyToggleButtonTapped() {
        PrivacyManager.shared.togglePrivacyMode()
        updatePrivacyButtonState()

        // Refresh display with current data
        if let displayModel = currentDisplayModel {
            updateDisplayWithPrivacy(displayModel)
        }

        // Notify other screens of the change
        NotificationCenter.default.post(name: NSNotification.Name("PrivacyModeChanged"), object: nil)
    }

    @objc private func privacyStateChanged() {
        updatePrivacyButtonState()

        // Refresh display with current data
        if let displayModel = currentDisplayModel {
            updateDisplayWithPrivacy(displayModel)
        }
    }

    private func updatePrivacyButtonState() {
        let isEnabled = PrivacyManager.shared.currentPrivacyState
        privacyToggleButton.isSelected = isEnabled
        privacyToggleButton.accessibilityValue = isEnabled ? "Hidden" : "Visible"
    }

    func updateUI(with displayModel: UIDisplayModel) {
        guard let displayModel = displayModel as? DisplayModel else { return }

        // Store the current display model for privacy toggle
        currentDisplayModel = displayModel
        updateDisplayWithPrivacy(displayModel)
    }

    private func updateDisplayWithPrivacy(_ displayModel: DisplayModel) {
        let isPrivacyEnabled = PrivacyManager.shared.currentPrivacyState

        // Update balance display
        var totalBalance = "-"
        if let totalBal = displayModel.totalBalance.toNumberOrNil() {
            totalBalance = isPrivacyEnabled ? "****" : totalBal.formatted(digits: 2)
        }
        totalBalanceLabel.text = totalBalance

        // Update currency display
        currencyLabel.text = displayModel.currency

        // Update gain/loss values
        let unrelalizedGLValue = displayModel.unrelalizedGLValue.toNumberOrNil() ?? 0.0
        let unrelalizedGLRate = displayModel.unrelalizedGLRate.toNumberOrNil() ?? 0.0
        let changeDirection = PriceChangeDirection.from(priceChange: unrelalizedGLValue,
                                                        percent: unrelalizedGLRate)

        if isPrivacyEnabled {
            unrealizedGLView.updateValueWithPrivacy(changeDirection: changeDirection)
            unrealizedGLPercentView.updateValueWithPrivacy(changeDirection: changeDirection)
        } else {
            unrealizedGLView.updateValue(unrelalizedGLValue, changeDirection: changeDirection)
            unrealizedGLPercentView.updateValue(unrelalizedGLRate, changeDirection: changeDirection)
        }

        distributionBarView.updateUI(with: displayModel.distributionData)
    }
    
    func setTexts() {
        titleLabel.text = "key0615".localized()
    }
}

// MARK: - Elements
extension WalletSummaryCell {
    
    struct DisplayModel: UIDisplayModel {
        let totalBalance: String?
        let unrelalizedGLRate: String?
        let unrelalizedGLValue: String?
        let currency: String?
        let distributionData: DistributionBarView.DisplayModel
    }
}

// MARK: - MarketExchange
extension MarketExchange: DistributionBarItem {
    public var barColor: UIColor {
        switch self {
        case .set:
            return Color.skyBlue
        case .nasdaq:
            return Color.vanilaPink
        }
    }
}
