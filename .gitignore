.history
.vscode
# === macOS 系统文件 ===
.DS_Store
.AppleDouble
.LSOverride

# === Xcode 相关 ===
*.xcworkspace
*.xcuserstate
*.xcuserdatad/
*.xcuserdata/
*.xcodeproj/xcuserdata/
*.moved-aside
DerivedData/
.idea/
*.hmap
*.ipa
*.xcscmblueprint

# === Swift Package Manager (SPM) ===
.build/
.swiftpm/
# 可选提交
Package.resolved

# === CocoaPods ===
Pods/
# 可选提交
Podfile.lock
*.xcconfig
Generated/

# === Carthage ===
Carthage/

# === Fastlane ===
fastlane/report.xml
fastlane/screenshots/
fastlane/test_output/

# === Archives and build output ===
*.xcarchive
*.xcodeproj/project.xcworkspace/xcuserdata/

# === Playground ===
timeline.xctimeline
playground.xcworkspace

# === Logs and temporary ===
*.log
*.tmp
*.swp
*~

# === SwiftLint / Code coverage ===
.swiftlint.yml
.swiftformat
.coverage/
*.xccovreport
*.xccovarchive
