//
//  PriceChangeView.swift
//  CUIModule
//
//  Created by <PERSON> on 16/01/2024.
//

import UIKit
import Core
import SharedData

public final class PriceChangeView: UIView, AnyView {
    
    // MARK: UI properties
    /**
     -----------
     [+ / –] | Price change
     -----------
     */
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 1)
        stackView.addArrangedSubviews([signImgView,
                                       valueLabel,
                                       arrowImgView])
        arrowImgView.snp.makeConstraints { $0.width.height.equalTo(12) }
        arrowImgView.isHidden = !showArrow
        
        return stackView
    }()
    
    private lazy var signImgView = UIImageView()
    private lazy var valueLabel = UILabel(font: Font.regular.of(size: 10))
    
    private lazy var arrowImgView = UIImageView()
    
    // MARK: Properties
    private var showArrow: Bool = false
    // MARK: Life cycle
    public init(showArrow: Bool = false) {
        super.init(frame: .zero)
        
        self.showArrow = showArrow
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        addSubview(contentStackView)
        contentStackView.snp.makeConstraints { $0.edges.equalToSuperview() }
    }
    
    public func updateValue(_ value: Double?, changeDirection: PriceChangeDirection) {
        signImgView.image = changeDirection.signIcon

        valueLabel.text = value == nil ? "–" : value!.formatted(sign: true, digits: 2)
        valueLabel.textColor = changeDirection.textColor

        arrowImgView.image = changeDirection.arrowIcon
    }

    public func updateValueWithPrivacy(changeDirection: PriceChangeDirection) {
        signImgView.image = changeDirection.signIcon

        valueLabel.text = "****"
        valueLabel.textColor = changeDirection.textColor

        arrowImgView.image = changeDirection.arrowIcon
    }
    
    public func updateUI(with displayModel: UIDisplayModel) {
        // Using MarketInstrument
        if let displayModel = displayModel as? MarketInstrument {
            let priceChange = displayModel.priceChange.toNumberOrNil()
            let percent = displayModel.priceChangePercentage.toNumberOrNil()
            let priceChangeDirection = PriceChangeDirection.from(priceChange: priceChange,
                                                                 percent: percent)
            
            updateValue(priceChange, changeDirection: priceChangeDirection)
        }
        
        // Using WatchListAsset
        if let displayModel = displayModel as? WatchListAsset {
            let priceChange = displayModel.valueChange.toNumberOrNil()
            let percent = displayModel.valueChangeRate.toNumberOrNil()
            let priceChangeDirection = PriceChangeDirection.from(priceChange: priceChange,
                                                                 percent: percent)
            
            updateValue(priceChange, changeDirection: priceChangeDirection)
        }
    }
}
