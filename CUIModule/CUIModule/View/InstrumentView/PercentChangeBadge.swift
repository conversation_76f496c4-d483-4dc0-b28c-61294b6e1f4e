//
//  PercentChangeBadge.swift
//  CUIModule
//
//  Created by <PERSON> on 16/01/2024.
//

import UIKit
import Core
import SharedData

public final class PercentChangeBadge: UIView, AnyView {
    
    // MARK: UI properties
    /**
     ------------------
     [+ / –] | Value | [%]
     ------------------
     */
    
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 2)
        stackView.addArrangedSubviews([signImgView,
                                       valueLabel,
                                       percentImgView])

        return stackView
    }()
    
    private lazy var signImgView = UIImageView()
    
    private lazy var valueLabel = UILabel(font: Font.regular.of(size: 10))
    
    private lazy var percentImgView = UIImageView()

    // MARK: Life cycle
    public init() {
        super.init(frame: .zero)
        
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        roundCorners(radius: 2)
        
        addSubview(contentStackView)
        contentStackView.snp.makeConstraints { make in
            make.top.equalTo(1).priority(.high)
            make.left.equalTo(3)
            make.bottom.equalTo(-1).priority(.high)
            make.right.equalTo(-3)
        }
    }
    
    public func updateValue(_ value: Double?, changeDirection: PriceChangeDirection) {
        backgroundColor = changeDirection.badgeBGColor

        signImgView.image = changeDirection.percentSignIcon

        valueLabel.text = value == nil ? "–" : value!.formatted(sign: true) + "%"
        valueLabel.textColor = changeDirection.textColor

        percentImgView.image = changeDirection.percentIcon
    }

    public func updateValueWithPrivacy(changeDirection: PriceChangeDirection) {
        backgroundColor = changeDirection.badgeBGColor

        signImgView.image = changeDirection.percentSignIcon

        valueLabel.text = "****"
        valueLabel.textColor = changeDirection.textColor

        percentImgView.image = changeDirection.percentIcon
    }
    
    public func updateUI(with displayModel: UIDisplayModel) {
        // Using MarketInstrument
        if let displayModel = displayModel as? MarketInstrument {
            let priceChange = displayModel.priceChange.toNumberOrNil()
            let percent = displayModel.priceChangePercentage.toNumberOrNil()
            let priceChangeDirection = PriceChangeDirection.from(priceChange: priceChange,
                                                                 percent: percent)
            
            updateValue(percent, changeDirection: priceChangeDirection)
        }
        
        // Using WatchListAsset
        if let displayModel = displayModel as? WatchListAsset {
            let priceChange = displayModel.valueChange.toNumberOrNil()
            let percent = displayModel.valueChangeRate.toNumberOrNil()
            let priceChangeDirection = PriceChangeDirection.from(priceChange: priceChange,
                                                                 percent: percent)
            
            updateValue(percent, changeDirection: priceChangeDirection)
        }
    }
}
