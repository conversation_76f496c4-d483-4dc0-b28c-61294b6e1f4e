//
//  SummaryItemCell.swift
//  WealthPlan
//
//  Created by <PERSON> on 13/11/24.
//

import UIKit
import Core
import SharedData

public final class SummaryItemCell: UITableViewCell, AnyView {
    
    // MARK: UI properties
    private lazy var contentStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 8)
        stackView.addArrangedSubviews([assetLogoView,
                                       infoStackView])
        assetLogoView.snp.makeConstraints { $0.width.height.equalTo(32) }
        
        return stackView
    }()
    
    private lazy var assetLogoView = UIImageView(contentMode: .scaleAspectFit,
                                                 cornerRadius: 16,
                                                 image: .image(named: "merit_ic_asset_placeholder"))
    private lazy var infoStackView = {
        let stackView = UIStackView(axis: .vertical,
                                    spacing: 4)
        
        stackView.addArrangedSubviews([upperStackView,
                                       underStackView])
        
        return stackView
    }()
    
    private lazy var upperStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([colorView,
                                       nameLabel,
                                       tagContainer,
                                       UIView(),
                                       marketValueLabel,
                                       currencyLabel,
                                       arrowView])
        colorView.snp.makeConstraints { $0.width.height.equalTo(8) }
        tagContainer.snp.makeConstraints { $0.height.equalTo(16) }
        
        [marketValueLabel,
         currencyLabel,
         arrowView]
            .forEach {
                $0.setContentCompressionResistancePriority(.required, for: .horizontal)
            }
        
        return stackView
    }()
    
    private lazy var colorView = UIView(cornerRadius: 4)
    
    private lazy var nameLabel = UILabel(font: Font.medium.of(size: 14),
                                         textColor: Color.txtTitle)
    
    private lazy var tagContainer = {
        let view = UIView(cornerRadius: 2)
        view.addSubview(tagLabel)
        tagLabel.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalTo(3)
            make.right.equalTo(-3)
        }
        tagLabel.setContentCompressionResistancePriority(.required, for: .horizontal)
        
        return view
    }()
    
    private lazy var tagLabel = UILabel(font: Font.medium.of(size: 12))
    
    private lazy var marketValueLabel = UILabel(font: Font.semiBold.of(size: 14))
    private lazy var currencyLabel = UILabel(font: Font.semiBold.of(size: 12))
    private lazy var arrowView = UIImageView()
    
    private lazy var underStackView = {
        let stackView = UIStackView(alignment: .center,
                                    spacing: 4)
        stackView.addArrangedSubviews([percentLabel,
                                       UIView(),
                                       unrealizedGlLabel,
                                       unrealizedGlRateBadge])
        
        return stackView
    }()
    
    private lazy var percentLabel = UILabel(font: Font.medium.of(size: 12),
                                            textColor: Color.txtParagraph)
    
    private lazy var unrealizedGlLabel = UILabel(font: Font.medium.of(size: 12))
    
    private lazy var unrealizedGlRateBadge = {
        let view = UIView(cornerRadius: 2)
        view.addSubview(unrealizedGlRateLabel)
        unrealizedGlRateLabel.snp.makeConstraints { make in
            make.top.equalTo(1)
            make.left.equalTo(3)
            make.bottom.equalTo(-1)
            make.right.equalTo(-3)
        }
        
        return view
    }()
    
    private lazy var unrealizedGlRateLabel = UILabel(font: Font.medium.of(size: 10))

    // MARK: Properties
    private var currentDisplayModel: DisplayModel?

    // MARK: Life cycle
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)

        setupUI()
        bindPrivacyChanges()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    public func setupUI() {
        contentView.addSubview(contentStackView)
        
        contentStackView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(12)
            make.right.equalTo(-12)
        }
    }

    private func bindPrivacyChanges() {
        // Listen for privacy state changes from other screens
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(privacyStateChanged),
            name: NSNotification.Name("PrivacyModeChanged"),
            object: nil
        )
    }

    @objc private func privacyStateChanged() {
        if let displayModel = currentDisplayModel {
            updateDisplayWithPrivacy(displayModel)
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    public func updateUI(with displayModel: UIDisplayModel) {
        guard let displayModel = displayModel as? DisplayModel else { return }

        currentDisplayModel = displayModel
        updateDisplayWithPrivacy(displayModel)
    }

    private func updateDisplayWithPrivacy(_ displayModel: DisplayModel) {
        let isPrivacyEnabled = PrivacyManager.shared.currentPrivacyState

        assetLogoView.setImage(with: displayModel.assetLogoUrl,
                               placeholder: .image(named: "merit_ic_asset_placeholder"))
        assetLogoView.isHidden = !displayModel.isAsset

        colorView.backgroundColor = displayModel.color
        colorView.isHidden = displayModel.isAsset

        nameLabel.text = displayModel.name

        // Tag
        if let riskLevel = RiskLevel(rawValue: displayModel.tag ?? "") {
            tagContainer.backgroundColor = riskLevel.bgColor
            tagLabel.textColor = riskLevel.chartColor

        } else {
            tagContainer.backgroundColor = Color.riskMedBg
            tagLabel.textColor = Color.riskMed
        }
        tagLabel.text = displayModel.tag
        tagContainer.isHidden = displayModel.tag?.isEmpty != false

        // Apply privacy masking to financial data
        marketValueLabel.text = isPrivacyEnabled ? "****" : displayModel.marketValue?.formatted(digits: 2)
        currencyLabel.text = isPrivacyEnabled ? "***" : displayModel.currency

        // Percent
        let percent = displayModel.percent?.formatted() ?? ""
        percentLabel.text = isPrivacyEnabled ? "****" : (percent.isEmpty ? "-" : percent + "%")

        // unrealizedGl
        unrealizedGlLabel.text = isPrivacyEnabled ? "****" : displayModel.unrealizedGl?.formatted(sign: true, digits: 2)

        // unrealizedGlRate
        let unrealizedGlRate = displayModel.unrealizedGlRate?.formatted(sign: true) ?? ""
        unrealizedGlRateLabel.text = isPrivacyEnabled ? "****" : (unrealizedGlRate.isEmpty ? "-" : unrealizedGlRate + "%")

        let numberQuality = NumberQuality(displayModel.unrealizedGlRate ?? 0)

        unrealizedGlRateBadge.backgroundColor = numberQuality.bgColor

        [marketValueLabel,
         currencyLabel,
         unrealizedGlLabel,
         unrealizedGlRateLabel]
            .forEach { $0.textColor = numberQuality.textColor }

        arrowView.image = numberQuality.arrowIcon
    }
}

// MARK: - Elements
public extension SummaryItemCell {
    
    struct DisplayModel: UIDisplayModel {
        public let isAsset: Bool
        public let assetLogoUrl: String?
        public let color: UIColor?
        public let name: String?
        public let tag: String?
        public let percent: Double?
        public let marketValue: Double?
        public let currency: String?
        public let unrealizedGl: Double?
        public let unrealizedGlRate: Double?
        
        public init(isAsset: Bool,
                    assetLogoUrl: String?,
                    color: UIColor?,
                    name: String?,
                    tag: String?,
                    percent: Double?,
                    marketValue: Double?,
                    currency: String?,
                    unrealizedGl: Double?,
                    unrealizedGlRate: Double?) {
            self.isAsset = isAsset
            self.assetLogoUrl = assetLogoUrl
            self.color = color
            self.name = name
            self.tag = tag
            self.percent = percent
            self.marketValue = marketValue
            self.currency = currency
            self.unrealizedGl = unrealizedGl
            self.unrealizedGlRate = unrealizedGlRate
        }
    }
}
